/*
 * Example Kernel (C Version)
 * Converted from example_kernel.asm to C with enhanced functionality
 */

// BIOS interface functions
void clear_screen(void);
void print_string(const char* str);
void print_char(char c);
void print_hex_word(unsigned short value);
char get_keystroke(void);
void reboot_system(void);
void read_command(char* buffer, int max_len);

// Function prototypes
void kernel_main(void);
void print_startup_feedback(void);
void print_banner(void);
void main_loop(void);
int strcmp_simple(const char* str1, const char* str2);
void handle_help(void);
void handle_test(void);
void handle_info(void);
void handle_reboot(void);
void handle_unknown(void);

// Global command buffer
static char command_buffer[64];

// Entry point called from assembly stub
void kernel_main(void) {
    // Print immediate visual feedback
    print_startup_feedback();
    
    // Clear screen and show startup message
    clear_screen();
    print_string("TEST\r\n");
    print_string("Kernel loaded successfully!\r\n");
    
    // Print banner
    print_banner();
    
    // Enter main command loop
    main_loop();
}

void print_startup_feedback(void) {
    // Print colored characters to show kernel is alive
    print_char('K');  // K for Kernel
    print_char('E');  // E for Entry  
    print_char('R');  // R for Running
    print_char('N');  // N for Now
}

void print_banner(void) {
    print_string("\r\n==============================\r\n");
    print_string("    DEBUG KERNEL v1.1\r\n");
    print_string("==============================\r\n");
}

void main_loop(void) {
    while (1) {
        // Print prompt
        print_string("> ");
        
        // Read command from user
        read_command(command_buffer, sizeof(command_buffer) - 1);
        
        // Handle commands
        if (strcmp_simple(command_buffer, "help") == 0) {
            handle_help();
        } else if (strcmp_simple(command_buffer, "test") == 0) {
            handle_test();
        } else if (strcmp_simple(command_buffer, "info") == 0) {
            handle_info();
        } else if (strcmp_simple(command_buffer, "reboot") == 0) {
            handle_reboot();
        } else if (command_buffer[0] == '\0') {
            // Empty command, just continue
            continue;
        } else {
            handle_unknown();
        }
    }
}

// Simple case-insensitive string comparison
int strcmp_simple(const char* str1, const char* str2) {
    while (*str1 && *str2) {
        char c1 = *str1;
        char c2 = *str2;
        
        // Convert to lowercase
        if (c1 >= 'A' && c1 <= 'Z') c1 += 32;
        if (c2 >= 'A' && c2 <= 'Z') c2 += 32;
        
        if (c1 != c2) return 0;  // Not equal
        
        str1++;
        str2++;
    }
    
    return (*str1 == *str2) ? 1 : 0;  // Equal if both at end
}

void handle_help(void) {
    print_string("Available commands: help, test, info, reboot\r\n");
}

void handle_test(void) {
    print_string("Kernel test passed!\r\n");
    print_string("All systems functional.\r\n");
}

void handle_info(void) {
    print_string("System Information:\r\n");
    print_string("  CS: 0x8000\r\n");
    print_string("  DS: 0x8000\r\n");
    print_string("  SS: 0x8000\r\n");
    print_string("  SP: 0xFFFE\r\n");
}

void handle_reboot(void) {
    print_string("Rebooting system...\r\n");
    
    // Simple delay
    for (volatile int i = 0; i < 50000; i++) {
        // Wait
    }
    
    reboot_system();
}

void handle_unknown(void) {
    print_string("Unknown command. Type \"help\" for available commands.\r\n");
}
