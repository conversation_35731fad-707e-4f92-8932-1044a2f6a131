BITS 16
ORG 0x0000

; ============================================================
;  Minimal Test Kernel
; ============================================================
start:
    ;---------------------------------------------------------
    ; Segment / stack setup - do this FIRST
    ;---------------------------------------------------------
    mov ax, 0x8000          ; Where Stage-2 dropped the kernel
    mov ds, ax              ; Data segment = code segment
    mov es, ax              ; Extra segment
    mov ss, ax              ; Stack segment
    mov sp, 0xFFFE          ; Stack pointer at top of segment

    cli                     ; Safe while we poke at flags
    cld                     ; Forward string ops
    sti

    ;---------------------------------------------------------
    ; Clear screen using BIOS
    ;---------------------------------------------------------
    mov ah, 0x00
    mov al, 0x03          ; 80x25 color text mode
    int 0x10

    ;---------------------------------------------------------
    ; Print test message to verify kernel is working
    ;---------------------------------------------------------
    mov ah, 0x0E
    mov al, 'K'
    int 0x10
    mov al, 'E'
    int 0x10
    mov al, 'R'
    int 0x10
    mov al, 'N'
    int 0x10
    mov al, 'E'
    int 0x10
    mov al, 'L'
    int 0x10
    mov al, ' '
    int 0x10
    mov al, 'O'
    int 0x10
    mov al, 'K'
    int 0x10
    mov al, '!'
    int 0x10
    mov al, 13
    int 0x10
    mov al, 10
    int 0x10

    ;---------------------------------------------------------
    ; Print banner
    ;---------------------------------------------------------
    mov al, '='
    int 0x10
    int 0x10
    int 0x10
    mov al, ' '
    int 0x10
    mov al, 'S'
    int 0x10
    mov al, 'I'
    int 0x10
    mov al, 'M'
    int 0x10
    mov al, 'P'
    int 0x10
    mov al, 'L'
    int 0x10
    mov al, 'E'
    int 0x10
    mov al, ' '
    int 0x10
    mov al, 'K'
    int 0x10
    mov al, 'E'
    int 0x10
    mov al, 'R'
    int 0x10
    mov al, 'N'
    int 0x10
    mov al, 'E'
    int 0x10
    mov al, 'L'
    int 0x10
    mov al, ' '
    int 0x10
    mov al, '='
    int 0x10
    int 0x10
    int 0x10
    mov al, 13
    int 0x10
    mov al, 10
    int 0x10

    ; Print prompt and start command loop
    jmp main_loop



; ============================================================
;  Main command loop
; ============================================================
main_loop:
    ; Print prompt
    mov ah, 0x0E
    mov al, '>'
    int 0x10
    mov al, ' '
    int 0x10

    ; Simple command input - just wait for a key and respond
    mov ah, 0x00
    int 0x16

    ; Save the character before echoing
    mov bl, al

    ; Echo the character
    mov ah, 0x0E
    int 0x10

    ; Print newline
    mov al, 13
    int 0x10
    mov al, 10
    int 0x10

    ; Check what was pressed (using saved character)
    cmp bl, 'h'
    je .help
    cmp bl, 't'
    je .test
    cmp bl, 'r'
    je .reboot

    ; Unknown command
    mov al, 'U'
    int 0x10
    mov al, 'n'
    int 0x10
    mov al, 'k'
    int 0x10
    mov al, 'n'
    int 0x10
    mov al, 'o'
    int 0x10
    mov al, 'w'
    int 0x10
    mov al, 'n'
    int 0x10
    mov al, 13
    int 0x10
    mov al, 10
    int 0x10
    jmp main_loop

.help:
    mov al, 'H'
    int 0x10
    mov al, 'e'
    int 0x10
    mov al, 'l'
    int 0x10
    mov al, 'p'
    int 0x10
    mov al, ':'
    int 0x10
    mov al, ' '
    int 0x10
    mov al, 'h'
    int 0x10
    mov al, '='
    int 0x10
    mov al, 'h'
    int 0x10
    mov al, 'e'
    int 0x10
    mov al, 'l'
    int 0x10
    mov al, 'p'
    int 0x10
    mov al, ','
    int 0x10
    mov al, ' '
    int 0x10
    mov al, 't'
    int 0x10
    mov al, '='
    int 0x10
    mov al, 't'
    int 0x10
    mov al, 'e'
    int 0x10
    mov al, 's'
    int 0x10
    mov al, 't'
    int 0x10
    mov al, ','
    int 0x10
    mov al, ' '
    int 0x10
    mov al, 'r'
    int 0x10
    mov al, '='
    int 0x10
    mov al, 'r'
    int 0x10
    mov al, 'e'
    int 0x10
    mov al, 'b'
    int 0x10
    mov al, 'o'
    int 0x10
    mov al, 'o'
    int 0x10
    mov al, 't'
    int 0x10
    mov al, 13
    int 0x10
    mov al, 10
    int 0x10
    jmp main_loop

.test:
    mov al, 'T'
    int 0x10
    mov al, 'e'
    int 0x10
    mov al, 's'
    int 0x10
    mov al, 't'
    int 0x10
    mov al, ' '
    int 0x10
    mov al, 'P'
    int 0x10
    mov al, 'A'
    int 0x10
    mov al, 'S'
    int 0x10
    mov al, 'S'
    int 0x10
    mov al, 'E'
    int 0x10
    mov al, 'D'
    int 0x10
    mov al, '!'
    int 0x10
    mov al, 13
    int 0x10
    mov al, 10
    int 0x10
    jmp main_loop

.reboot:
    mov al, 'R'
    int 0x10
    mov al, 'e'
    int 0x10
    mov al, 'b'
    int 0x10
    mov al, 'o'
    int 0x10
    mov al, 'o'
    int 0x10
    mov al, 't'
    int 0x10
    mov al, 'i'
    int 0x10
    mov al, 'n'
    int 0x10
    mov al, 'g'
    int 0x10
    mov al, '.'
    int 0x10
    mov al, '.'
    int 0x10
    mov al, '.'
    int 0x10
    mov al, 13
    int 0x10
    mov al, 10
    int 0x10

    ; Wait a moment
    mov cx, 0xFFFF
.delay:
    nop
    loop .delay

    ; Reboot via keyboard controller
    mov al, 0xFE
    out 0x64, al

    ; Fallback: triple fault
    int 3
    jmp $

; ============================================================
;  Data section (minimal - we don't use strings anymore)
; ============================================================

; Pad to fill exactly three 512-byte sectors (1536 bytes)
times 1536-($-$$) db 0