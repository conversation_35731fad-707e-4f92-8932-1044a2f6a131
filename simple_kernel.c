/*
 * Simple Kernel (C Version)
 * Converted from simple_kernel.asm to C while maintaining the same functionality
 */

// BIOS interface functions
void clear_screen(void);
void print_string(const char* str);
void print_char(char c);
char get_keystroke(void);
void reboot_system(void);

// Function prototypes
void kernel_main(void);
void setup_segments(void);
void print_banner(void);
void main_loop(void);
void handle_help(void);
void handle_test(void);
void handle_reboot(void);
void handle_unknown(void);

// Entry point called from assembly stub
void kernel_main(void) {
    // Setup is done in assembly stub
    
    // Clear screen
    clear_screen();
    
    // Print startup message
    print_string("KERNEL OK!\r\n");
    
    // Print banner
    print_banner();
    
    // Enter main command loop
    main_loop();
}

void print_banner(void) {
    print_string("=== SIMPLE KERNEL ===\r\n");
}

void main_loop(void) {
    char key;
    
    while (1) {
        // Print prompt
        print_string("> ");
        
        // Get user input
        key = get_keystroke();
        
        // Echo the character
        print_char(key);
        print_string("\r\n");
        
        // Handle commands
        switch (key) {
            case 'h':
            case 'H':
                handle_help();
                break;
            case 't':
            case 'T':
                handle_test();
                break;
            case 'r':
            case 'R':
                handle_reboot();
                break;
            default:
                handle_unknown();
                break;
        }
    }
}

void handle_help(void) {
    print_string("Help: h=help, t=test, r=reboot\r\n");
}

void handle_test(void) {
    print_string("Test PASSED!\r\n");
}

void handle_reboot(void) {
    print_string("Rebooting...\r\n");
    
    // Simple delay
    for (volatile int i = 0; i < 50000; i++) {
        // Wait
    }
    
    reboot_system();
}

void handle_unknown(void) {
    print_string("Unknown\r\n");
}
